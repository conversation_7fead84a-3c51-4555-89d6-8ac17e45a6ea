'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useRestaurant } from '../providers'
import { formatCurrency } from '@/lib/utils'
import { Category, MenuItem } from '@/types'
import { ArrowRight, ShoppingCart } from 'lucide-react'
import Link from 'next/link'

export default function MenuPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const { settings } = useRestaurant()

  useEffect(() => {
    fetchMenuData()
  }, [])

  const fetchMenuData = async () => {
    try {
      const [categoriesRes, menuItemsRes] = await Promise.all([
        fetch('/api/categories'),
        fetch('/api/menu')
      ])

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData.categories || [])
      }

      if (menuItemsRes.ok) {
        const menuData = await menuItemsRes.json()
        setMenuItems(menuData.menuItems || [])
      }
    } catch (error) {
      console.error('Error fetching menu data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredItems = selectedCategory === 'all' 
    ? menuItems.filter(item => item.isActive && item.isAvailable)
    : menuItems.filter(item => 
        item.categoryId === selectedCategory && 
        item.isActive && 
        item.isAvailable
      )

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowRight className="h-4 w-4 mr-2" />
                  العودة
                </Button>
              </Link>
              {settings?.logo && (
                <img src={settings.logo} alt={settings.name} className="h-8 w-8" />
              )}
              <h1 className="text-xl font-bold text-gray-900">
                قائمة {settings?.name || 'المطعم'}
              </h1>
            </div>
            <Link href="/pos">
              <Button>
                <ShoppingCart className="h-4 w-4 mr-2" />
                طلب جديد
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Restaurant Info */}
        {settings && (
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              {settings.name}
            </h2>
            {settings.description && (
              <p className="text-gray-600 mb-4">{settings.description}</p>
            )}
            <div className="flex justify-center space-x-4 text-sm text-gray-500">
              {settings.phone && <span>📞 {settings.phone}</span>}
              {settings.address && <span>📍 {settings.address}</span>}
            </div>
          </div>
        )}

        {/* Category Filter */}
        <div className="flex flex-wrap gap-2 mb-8 justify-center">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            onClick={() => setSelectedCategory('all')}
            size="sm"
          >
            جميع الأصناف
          </Button>
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? 'default' : 'outline'}
              onClick={() => setSelectedCategory(category.id)}
              size="sm"
            >
              {category.name}
            </Button>
          ))}
        </div>

        {/* Menu Items */}
        {filteredItems.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">لا توجد أصناف متاحة حالياً</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredItems.map((item) => (
              <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                {item.image && (
                  <div className="aspect-video bg-gray-200">
                    <img
                      src={item.image}
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{item.name}</CardTitle>
                    <Badge variant="secondary">
                      {formatCurrency(item.price, settings?.currency)}
                    </Badge>
                  </div>
                  {item.description && (
                    <CardDescription className="text-sm">
                      {item.description}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-primary">
                      {formatCurrency(item.price, settings?.currency)}
                    </span>
                    <Badge variant={item.isAvailable ? 'default' : 'secondary'}>
                      {item.isAvailable ? 'متوفر' : 'غير متوفر'}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Categories Section */}
        {selectedCategory === 'all' && categories.length > 0 && (
          <div className="mt-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              فئات القائمة
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {categories.map((category) => (
                <Card 
                  key={category.id} 
                  className="cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.image && (
                    <div className="aspect-video bg-gray-200">
                      <img
                        src={category.image}
                        alt={category.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <CardHeader>
                    <CardTitle className="text-center">{category.name}</CardTitle>
                    {category.description && (
                      <CardDescription className="text-center">
                        {category.description}
                      </CardDescription>
                    )}
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
