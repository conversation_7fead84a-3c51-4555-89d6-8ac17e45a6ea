// Restaurant Management System - JavaScript
class RestaurantSystem {
    constructor() {
        this.currentUser = null;
        this.currentPage = 'menu';
        this.isLoggedIn = false;
        this.orders = [];
        this.menuItems = [];
        this.cart = [];
        this.settings = {
            restaurantName: 'مطعم الذواقة',
            currency: 'ر.س',
            taxRate: 0.15,
            deliveryFee: 15
        };

        this.initializeData();
        this.bindEvents();
        this.loadPublicMenu();
        this.loadRecentOrders();
        this.loadTopItems();
    }

    initializeData() {
        // Sample menu items
        this.menuItems = [
            { id: 1, name: 'كبسة لحم', price: 45, category: 'أطباق رئيسية', image: '🍖', available: true },
            { id: 2, name: 'مندي دجاج', price: 35, category: 'أطباق رئيسية', image: '🍗', available: true },
            { id: 3, name: 'حمص بالطحينة', price: 15, category: 'مقبلات', image: '🥙', available: true },
            { id: 4, name: 'متبل باذنجان', price: 18, category: 'مقبلات', image: '🍆', available: true },
            { id: 5, name: 'عصير برتقال', price: 12, category: 'مشروبات', image: '🍊', available: true },
            { id: 6, name: 'شاي أحمر', price: 8, category: 'مشروبات', image: '🍵', available: true },
            { id: 7, name: 'كنافة نابلسية', price: 25, category: 'حلويات', image: '🧁', available: true },
            { id: 8, name: 'بقلاوة', price: 20, category: 'حلويات', image: '🥮', available: true }
        ];

        // Sample orders
        this.orders = [
            {
                id: 1001,
                customerName: 'أحمد محمد',
                items: [
                    { name: 'كبسة لحم', quantity: 2, price: 45 },
                    { name: 'عصير برتقال', quantity: 2, price: 12 }
                ],
                total: 114,
                status: 'جاري التحضير',
                time: '10:30 ص',
                type: 'تناول في المطعم'
            },
            {
                id: 1002,
                customerName: 'فاطمة علي',
                items: [
                    { name: 'مندي دجاج', quantity: 1, price: 35 },
                    { name: 'حمص بالطحينة', quantity: 1, price: 15 }
                ],
                total: 50,
                status: 'جاهز',
                time: '10:45 ص',
                type: 'استلام'
            },
            {
                id: 1003,
                customerName: 'محمد سالم',
                items: [
                    { name: 'كنافة نابلسية', quantity: 3, price: 25 }
                ],
                total: 75,
                status: 'تم التسليم',
                time: '11:00 ص',
                type: 'توصيل'
            }
        ];
    }

    bindEvents() {
        // Login form
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Login button
        document.getElementById('loginBtn').addEventListener('click', () => {
            this.showLoginModal();
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.handleLogout();
        });

        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', () => {
            this.toggleSidebar();
        });

        // Menu items
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.getAttribute('data-page');
                this.showPage(page);
            });
        });

        // Category filter buttons for public menu
        document.querySelectorAll('.category-filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.category-filter-btn').forEach(b => {
                    b.classList.remove('active', 'bg-orange-500', 'text-white');
                    b.classList.add('bg-gray-200', 'text-gray-700');
                });

                e.target.classList.add('active', 'bg-orange-500', 'text-white');
                e.target.classList.remove('bg-gray-200', 'text-gray-700');

                this.filterPublicMenu(e.target.dataset.category);
            });
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            if (window.innerWidth < 1024 &&
                !sidebar.contains(e.target) &&
                !sidebarToggle.contains(e.target)) {
                sidebar.classList.add('-translate-x-full');
            }
        });

        // Close login modal when clicking outside
        document.getElementById('loginModal').addEventListener('click', (e) => {
            if (e.target.id === 'loginModal') {
                this.hideLoginModal();
            }
        });
    }

    showLoginModal() {
        document.getElementById('loginModal').classList.remove('hidden');
    }

    hideLoginModal() {
        document.getElementById('loginModal').classList.add('hidden');
    }

    handleLogin() {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        // Simple validation (in real app, this would be server-side)
        if (email === '<EMAIL>' && password === 'admin123') {
            this.currentUser = { name: 'مدير النظام', email: email };
            this.isLoggedIn = true;

            // Hide login modal and show admin sections
            this.hideLoginModal();
            document.getElementById('adminSection').classList.remove('hidden');
            document.getElementById('userInfo').classList.remove('hidden');
            document.getElementById('loginBtn').classList.add('hidden');
            document.getElementById('logoutBtn').classList.remove('hidden');

            this.showNotification('تم تسجيل الدخول بنجاح! يمكنك الآن الوصول لجميع الوظائف الإدارية', 'success');

            // Switch to dashboard
            this.showPage('dashboard');
        } else {
            this.showNotification('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
        }
    }

    handleLogout() {
        this.currentUser = null;
        this.isLoggedIn = false;

        // Hide admin sections and show login button
        document.getElementById('adminSection').classList.add('hidden');
        document.getElementById('userInfo').classList.add('hidden');
        document.getElementById('loginBtn').classList.remove('hidden');
        document.getElementById('logoutBtn').classList.add('hidden');

        // Reset form
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('password').value = 'admin123';

        this.showNotification('تم تسجيل الخروج بنجاح', 'success');

        // Go back to menu
        this.showPage('menu');
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('-translate-x-full');
    }

    showPage(pageName) {
        // Check if admin page and user not logged in
        const adminPages = ['dashboard', 'orders', 'customers', 'reports', 'coupons', 'delivery', 'settings'];
        if (adminPages.includes(pageName) && !this.isLoggedIn) {
            this.showNotification('يجب تسجيل الدخول للوصول لهذه الصفحة', 'error');
            this.showLoginModal();
            return;
        }

        // Hide all pages
        document.querySelectorAll('.page-content').forEach(page => {
            page.classList.add('hidden');
        });

        // Remove active class from all menu items
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        // Show selected page
        const pageElement = document.getElementById(pageName + 'Page');
        if (pageElement) {
            pageElement.classList.remove('hidden');
        } else {
            // If page doesn't exist, create it dynamically
            this.createPage(pageName);
        }

        // Add active class to selected menu item
        const menuItem = document.querySelector(`[data-page="${pageName}"]`);
        if (menuItem) {
            menuItem.classList.add('active');
        }

        this.currentPage = pageName;

        // Close sidebar on mobile after selection
        if (window.innerWidth < 1024) {
            document.getElementById('sidebar').classList.add('-translate-x-full');
        }

        // Load page-specific data
        if (pageName === 'menu') {
            this.loadPublicMenu();
        }
    }

    loadPublicMenu() {
        const container = document.getElementById('publicMenuGrid');
        if (!container) return;

        container.innerHTML = this.menuItems.map(item => `
            <div class="menu-item-card bg-white rounded-2xl shadow-lg overflow-hidden card-hover" data-category="${item.category}">
                <div class="p-6 text-center">
                    <div class="text-6xl mb-4">${item.image}</div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">${item.name}</h3>
                    <p class="text-sm text-gray-600 mb-3">${item.category}</p>
                    <div class="flex items-center justify-center mb-4">
                        <span class="text-2xl font-bold text-orange-600">${item.price} ${this.settings.currency}</span>
                    </div>
                    <div class="flex items-center justify-center mb-4">
                        <span class="px-3 py-1 text-sm rounded-full ${item.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${item.available ? 'متوفر' : 'غير متوفر'}
                        </span>
                    </div>
                    <button class="w-full btn-primary text-white py-3 rounded-lg font-semibold ${item.available ? '' : 'opacity-50 cursor-not-allowed'}"
                            ${item.available ? `onclick="restaurantSystem.quickOrder(${item.id})"` : 'disabled'}>
                        ${item.available ? 'طلب سريع' : 'غير متوفر'}
                    </button>
                </div>
            </div>
        `).join('');
    }

    filterPublicMenu(category) {
        const items = document.querySelectorAll('.menu-item-card');
        items.forEach(item => {
            const itemCategory = item.dataset.category;

            if (category === 'all' || itemCategory === category) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    quickOrder(itemId) {
        const menuItem = this.menuItems.find(item => item.id === itemId);
        if (!menuItem || !menuItem.available) return;

        // Add to cart
        const existingItem = this.cart.find(item => item.id === itemId);
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.cart.push({
                id: itemId,
                name: menuItem.name,
                price: menuItem.price,
                quantity: 1
            });
        }

        this.showNotification(`تم إضافة ${menuItem.name} إلى السلة! انتقل لنقطة البيع لإتمام الطلب`, 'success');

        // Show notification with option to go to POS
        setTimeout(() => {
            if (confirm('هل تريد الانتقال لنقطة البيع لإتمام الطلب؟')) {
                this.showPage('pos');
            }
        }, 1000);
    }

    createPage(pageName) {
        const main = document.querySelector('main');
        let pageContent = '';

        switch (pageName) {
            case 'pos':
                pageContent = this.createPOSPage();
                break;
            case 'menu':
                pageContent = this.createMenuPage();
                break;
            case 'orders':
                pageContent = this.createOrdersPage();
                break;
            case 'customers':
                pageContent = this.createCustomersPage();
                break;
            case 'reports':
                pageContent = this.createReportsPage();
                break;
            case 'coupons':
                pageContent = this.createCouponsPage();
                break;
            case 'delivery':
                pageContent = this.createDeliveryPage();
                break;
            case 'settings':
                pageContent = this.createSettingsPage();
                break;
            default:
                pageContent = '<div class="p-6"><h2 class="text-2xl font-bold">الصفحة قيد التطوير</h2></div>';
        }

        const pageDiv = document.createElement('div');
        pageDiv.id = pageName + 'Page';
        pageDiv.className = 'page-content';
        pageDiv.innerHTML = pageContent;
        main.appendChild(pageDiv);

        // Bind events for the new page
        this.bindPageEvents(pageName);
    }

    createPOSPage() {
        return `
            <div class="p-6">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">نقطة البيع</h2>
                    <p class="text-gray-600">إنشاء طلبات جديدة</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Menu Items -->
                    <div class="lg:col-span-2">
                        <div class="mb-6">
                            <div class="flex space-x-2 mb-4">
                                <button class="category-btn active px-4 py-2 rounded-lg bg-orange-500 text-white" data-category="all">
                                    جميع الأصناف
                                </button>
                                <button class="category-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300" data-category="أطباق رئيسية">
                                    أطباق رئيسية
                                </button>
                                <button class="category-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300" data-category="مقبلات">
                                    مقبلات
                                </button>
                                <button class="category-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300" data-category="مشروبات">
                                    مشروبات
                                </button>
                                <button class="category-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300" data-category="حلويات">
                                    حلويات
                                </button>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4" id="menuItemsGrid">
                            ${this.menuItems.map(item => `
                                <div class="pos-item bg-white rounded-lg shadow-md p-4 ${item.available ? '' : 'opacity-50'}" data-item-id="${item.id}">
                                    <div class="text-center">
                                        <div class="text-4xl mb-2">${item.image}</div>
                                        <h3 class="font-semibold text-gray-900 mb-1">${item.name}</h3>
                                        <p class="text-sm text-gray-600 mb-2">${item.category}</p>
                                        <p class="text-lg font-bold text-orange-600 mb-3">${item.price} ${this.settings.currency}</p>
                                        <button class="add-to-cart-btn w-full btn-primary text-white py-2 rounded-lg font-semibold ${item.available ? '' : 'cursor-not-allowed'}" 
                                                ${item.available ? '' : 'disabled'} data-item-id="${item.id}">
                                            ${item.available ? 'إضافة للسلة' : 'غير متوفر'}
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Cart -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-lg shadow-lg p-6 sticky top-24">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">
                                <i class="fas fa-shopping-cart text-orange-500 mr-2"></i>
                                السلة
                            </h3>

                            <!-- Customer Info -->
                            <div class="mb-6 space-y-3">
                                <input type="text" id="customerName" placeholder="اسم العميل (اختياري)" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                                <input type="tel" id="customerPhone" placeholder="رقم الهاتف (اختياري)" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                                <select id="orderType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                                    <option value="dine-in">تناول في المطعم</option>
                                    <option value="takeaway">استلام</option>
                                    <option value="delivery">توصيل</option>
                                </select>
                            </div>

                            <!-- Cart Items -->
                            <div id="cartItems" class="space-y-3 mb-6 max-h-64 overflow-y-auto">
                                <p class="text-gray-500 text-center py-8">السلة فارغة</p>
                            </div>

                            <!-- Order Summary -->
                            <div id="orderSummary" class="border-t pt-4 space-y-2 hidden">
                                <div class="flex justify-between text-sm">
                                    <span>المجموع الفرعي:</span>
                                    <span id="subtotal">0 ${this.settings.currency}</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>الضريبة (15%):</span>
                                    <span id="tax">0 ${this.settings.currency}</span>
                                </div>
                                <div class="flex justify-between font-bold text-lg border-t pt-2">
                                    <span>الإجمالي:</span>
                                    <span id="total">0 ${this.settings.currency}</span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="space-y-3 mt-6">
                                <button id="processOrderBtn" class="w-full btn-primary text-white py-3 rounded-lg font-semibold disabled:opacity-50" disabled>
                                    <i class="fas fa-check mr-2"></i>
                                    إتمام الطلب
                                </button>
                                <button id="clearCartBtn" class="w-full bg-gray-500 text-white py-2 rounded-lg font-semibold hover:bg-gray-600 hidden">
                                    <i class="fas fa-trash mr-2"></i>
                                    مسح السلة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindPageEvents(pageName) {
        if (pageName === 'pos') {
            this.bindPOSEvents();
        }
    }

    bindPOSEvents() {
        // Category filter buttons
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.category-btn').forEach(b => {
                    b.classList.remove('active', 'bg-orange-500', 'text-white');
                    b.classList.add('bg-gray-200', 'text-gray-700');
                });
                
                e.target.classList.add('active', 'bg-orange-500', 'text-white');
                e.target.classList.remove('bg-gray-200', 'text-gray-700');
                
                this.filterMenuItems(e.target.dataset.category);
            });
        });

        // Add to cart buttons
        document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const itemId = parseInt(e.target.dataset.itemId);
                this.addToCart(itemId);
            });
        });

        // Process order button
        document.getElementById('processOrderBtn').addEventListener('click', () => {
            this.processOrder();
        });

        // Clear cart button
        document.getElementById('clearCartBtn').addEventListener('click', () => {
            this.clearCart();
        });
    }

    filterMenuItems(category) {
        const items = document.querySelectorAll('.pos-item');
        items.forEach(item => {
            const itemId = parseInt(item.dataset.itemId);
            const menuItem = this.menuItems.find(mi => mi.id === itemId);
            
            if (category === 'all' || menuItem.category === category) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    addToCart(itemId) {
        const menuItem = this.menuItems.find(item => item.id === itemId);
        if (!menuItem || !menuItem.available) return;

        const existingItem = this.cart.find(item => item.id === itemId);
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.cart.push({
                id: itemId,
                name: menuItem.name,
                price: menuItem.price,
                quantity: 1
            });
        }

        this.updateCartDisplay();
        this.showNotification(`تم إضافة ${menuItem.name} إلى السلة`, 'success');
    }

    updateCartDisplay() {
        const cartItemsContainer = document.getElementById('cartItems');
        const orderSummary = document.getElementById('orderSummary');
        const processOrderBtn = document.getElementById('processOrderBtn');
        const clearCartBtn = document.getElementById('clearCartBtn');

        if (this.cart.length === 0) {
            cartItemsContainer.innerHTML = '<p class="text-gray-500 text-center py-8">السلة فارغة</p>';
            orderSummary.classList.add('hidden');
            processOrderBtn.disabled = true;
            clearCartBtn.classList.add('hidden');
            return;
        }

        // Show cart items
        cartItemsContainer.innerHTML = this.cart.map(item => `
            <div class="cart-item p-3 rounded-lg">
                <div class="flex justify-between items-center">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900">${item.name}</h4>
                        <p class="text-sm text-gray-600">${item.price} ${this.settings.currency} × ${item.quantity}</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="quantity-btn bg-orange-500 text-white w-8 h-8 rounded-full hover:bg-orange-600" 
                                onclick="restaurantSystem.updateCartItemQuantity(${item.id}, ${item.quantity - 1})">
                            <i class="fas fa-minus text-xs"></i>
                        </button>
                        <span class="font-semibold px-2">${item.quantity}</span>
                        <button class="quantity-btn bg-orange-500 text-white w-8 h-8 rounded-full hover:bg-orange-600" 
                                onclick="restaurantSystem.updateCartItemQuantity(${item.id}, ${item.quantity + 1})">
                            <i class="fas fa-plus text-xs"></i>
                        </button>
                        <button class="remove-btn bg-red-500 text-white w-8 h-8 rounded-full hover:bg-red-600 mr-2" 
                                onclick="restaurantSystem.removeFromCart(${item.id})">
                            <i class="fas fa-trash text-xs"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        // Calculate totals
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = subtotal * this.settings.taxRate;
        const total = subtotal + tax;

        // Update summary
        document.getElementById('subtotal').textContent = `${subtotal.toFixed(2)} ${this.settings.currency}`;
        document.getElementById('tax').textContent = `${tax.toFixed(2)} ${this.settings.currency}`;
        document.getElementById('total').textContent = `${total.toFixed(2)} ${this.settings.currency}`;

        orderSummary.classList.remove('hidden');
        processOrderBtn.disabled = false;
        clearCartBtn.classList.remove('hidden');
    }

    updateCartItemQuantity(itemId, newQuantity) {
        if (newQuantity <= 0) {
            this.removeFromCart(itemId);
            return;
        }

        const cartItem = this.cart.find(item => item.id === itemId);
        if (cartItem) {
            cartItem.quantity = newQuantity;
            this.updateCartDisplay();
        }
    }

    removeFromCart(itemId) {
        this.cart = this.cart.filter(item => item.id !== itemId);
        this.updateCartDisplay();
        this.showNotification('تم حذف الصنف من السلة', 'success');
    }

    clearCart() {
        this.cart = [];
        this.updateCartDisplay();
        this.showNotification('تم مسح السلة', 'success');
    }

    processOrder() {
        if (this.cart.length === 0) return;

        const customerName = document.getElementById('customerName').value || 'عميل';
        const customerPhone = document.getElementById('customerPhone').value || '';
        const orderType = document.getElementById('orderType').value;

        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = subtotal * this.settings.taxRate;
        const total = subtotal + tax;

        const newOrder = {
            id: Date.now(),
            customerName,
            customerPhone,
            items: [...this.cart],
            subtotal,
            tax,
            total,
            status: 'جديد',
            time: new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' }),
            type: this.getOrderTypeText(orderType),
            date: new Date().toLocaleDateString('ar-SA')
        };

        this.orders.unshift(newOrder);
        this.clearCart();
        
        // Reset customer info
        document.getElementById('customerName').value = '';
        document.getElementById('customerPhone').value = '';
        document.getElementById('orderType').value = 'dine-in';

        this.showNotification(`تم إنشاء الطلب رقم ${newOrder.id} بنجاح!`, 'success');
        
        // Update stats
        this.updateStats();
    }

    getOrderTypeText(type) {
        const types = {
            'dine-in': 'تناول في المطعم',
            'takeaway': 'استلام',
            'delivery': 'توصيل'
        };
        return types[type] || 'تناول في المطعم';
    }

    updateStats() {
        const totalOrders = this.orders.length;
        const totalRevenue = this.orders.reduce((sum, order) => sum + order.total, 0);
        
        const today = new Date().toLocaleDateString('ar-SA');
        const todayOrders = this.orders.filter(order => order.date === today);
        const todayOrdersCount = todayOrders.length;
        const todayRevenue = todayOrders.reduce((sum, order) => sum + order.total, 0);

        document.getElementById('totalOrders').textContent = totalOrders;
        document.getElementById('totalRevenue').textContent = `${totalRevenue.toFixed(2)} ${this.settings.currency}`;
        document.getElementById('todayOrders').textContent = todayOrdersCount;
        document.getElementById('todayRevenue').textContent = `${todayRevenue.toFixed(2)} ${this.settings.currency}`;
    }

    loadRecentOrders() {
        const container = document.getElementById('recentOrders');
        if (!container) return;

        const recentOrders = this.orders.slice(0, 5);
        container.innerHTML = recentOrders.map(order => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex-1">
                    <p class="font-semibold text-gray-900">${order.customerName}</p>
                    <p class="text-sm text-gray-600">${order.items.length} صنف - ${order.total.toFixed(2)} ${this.settings.currency}</p>
                    <p class="text-xs text-gray-500">${order.time} - ${order.type}</p>
                </div>
                <div class="text-left">
                    <span class="px-2 py-1 text-xs rounded-full ${this.getStatusColor(order.status)}">
                        ${order.status}
                    </span>
                </div>
            </div>
        `).join('');
    }

    loadTopItems() {
        const container = document.getElementById('topItems');
        if (!container) return;

        // Calculate item popularity
        const itemStats = {};
        this.orders.forEach(order => {
            order.items.forEach(item => {
                if (itemStats[item.name]) {
                    itemStats[item.name] += item.quantity;
                } else {
                    itemStats[item.name] = item.quantity;
                }
            });
        });

        const topItems = Object.entries(itemStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        container.innerHTML = topItems.map(([name, count]) => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex-1">
                    <p class="font-semibold text-gray-900">${name}</p>
                    <p class="text-sm text-gray-600">تم طلبه ${count} مرة</p>
                </div>
                <div class="text-yellow-500">
                    <i class="fas fa-star"></i>
                </div>
            </div>
        `).join('');
    }

    getStatusColor(status) {
        const colors = {
            'جديد': 'bg-blue-100 text-blue-800',
            'جاري التحضير': 'bg-yellow-100 text-yellow-800',
            'جاهز': 'bg-green-100 text-green-800',
            'تم التسليم': 'bg-gray-100 text-gray-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    }

    showNotification(message, type = 'success') {
        const notification = document.getElementById('notification');
        const notificationText = document.getElementById('notificationText');
        
        notificationText.textContent = message;
        notification.className = `notification ${type}`;
        notification.classList.add('show');

        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
}

// Initialize the system
const restaurantSystem = new RestaurantSystem();

    createMenuPage() {
        return `
            <div class="p-6">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">إدارة القائمة</h2>
                    <p class="text-gray-600">إضافة وتعديل أصناف الطعام</p>
                </div>

                <div class="mb-6">
                    <button class="btn-primary text-white px-6 py-3 rounded-lg font-semibold" onclick="restaurantSystem.showAddItemModal()">
                        <i class="fas fa-plus mr-2"></i>
                        إضافة صنف جديد
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    ${this.menuItems.map(item => `
                        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                            <div class="text-center">
                                <div class="text-4xl mb-3">${item.image}</div>
                                <h3 class="font-bold text-gray-900 mb-2">${item.name}</h3>
                                <p class="text-sm text-gray-600 mb-2">${item.category}</p>
                                <p class="text-xl font-bold text-orange-600 mb-4">${item.price} ${this.settings.currency}</p>
                                <div class="flex items-center justify-center mb-4">
                                    <span class="px-3 py-1 text-xs rounded-full ${item.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                        ${item.available ? 'متوفر' : 'غير متوفر'}
                                    </span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="flex-1 bg-blue-500 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-600"
                                            onclick="restaurantSystem.editMenuItem(${item.id})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="flex-1 ${item.available ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-green-500 hover:bg-green-600'} text-white py-2 px-3 rounded-lg text-sm"
                                            onclick="restaurantSystem.toggleItemAvailability(${item.id})">
                                        <i class="fas ${item.available ? 'fa-eye-slash' : 'fa-eye'}"></i>
                                    </button>
                                    <button class="flex-1 bg-red-500 text-white py-2 px-3 rounded-lg text-sm hover:bg-red-600"
                                            onclick="restaurantSystem.deleteMenuItem(${item.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    createOrdersPage() {
        return `
            <div class="p-6">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">إدارة الطلبات</h2>
                    <p class="text-gray-600">متابعة ومعالجة الطلبات</p>
                </div>

                <div class="mb-6">
                    <div class="flex space-x-4">
                        <button class="order-filter-btn active px-4 py-2 rounded-lg bg-orange-500 text-white" data-status="all">
                            جميع الطلبات
                        </button>
                        <button class="order-filter-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300" data-status="جديد">
                            طلبات جديدة
                        </button>
                        <button class="order-filter-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300" data-status="جاري التحضير">
                            قيد التحضير
                        </button>
                        <button class="order-filter-btn px-4 py-2 rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300" data-status="جاهز">
                            جاهز للتسليم
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" id="ordersGrid">
                    ${this.orders.map(order => `
                        <div class="bg-white rounded-lg shadow-md p-6 card-hover order-card" data-status="${order.status}">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h3 class="font-bold text-gray-900">طلب #${order.id}</h3>
                                    <p class="text-sm text-gray-600">${order.customerName}</p>
                                    <p class="text-xs text-gray-500">${order.time} - ${order.type}</p>
                                </div>
                                <span class="px-3 py-1 text-xs rounded-full ${this.getStatusColor(order.status)}">
                                    ${order.status}
                                </span>
                            </div>

                            <div class="space-y-2 mb-4">
                                ${order.items.map(item => `
                                    <div class="flex justify-between text-sm">
                                        <span>${item.name} × ${item.quantity}</span>
                                        <span>${(item.price * item.quantity).toFixed(2)} ${this.settings.currency}</span>
                                    </div>
                                `).join('')}
                            </div>

                            <div class="border-t pt-3 mb-4">
                                <div class="flex justify-between font-bold">
                                    <span>الإجمالي:</span>
                                    <span>${order.total.toFixed(2)} ${this.settings.currency}</span>
                                </div>
                            </div>

                            <div class="flex space-x-2">
                                ${order.status !== 'تم التسليم' ? `
                                    <button class="flex-1 bg-green-500 text-white py-2 px-3 rounded-lg text-sm hover:bg-green-600"
                                            onclick="restaurantSystem.updateOrderStatus(${order.id}, 'next')">
                                        ${this.getNextStatusText(order.status)}
                                    </button>
                                ` : ''}
                                <button class="flex-1 bg-blue-500 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-600"
                                        onclick="restaurantSystem.printOrder(${order.id})">
                                    <i class="fas fa-print"></i> طباعة
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    createCustomersPage() {
        return `
            <div class="p-6">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">إدارة العملاء</h2>
                    <p class="text-gray-600">قائمة العملاء وتاريخ طلباتهم</p>
                </div>

                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">قائمة العملاء</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم العميل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الهاتف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الطلبات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي المبلغ</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر طلب</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                ${this.getCustomerStats().map(customer => `
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${customer.name}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${customer.phone || 'غير محدد'}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${customer.orderCount}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${customer.totalSpent.toFixed(2)} ${this.settings.currency}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${customer.lastOrder}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    createReportsPage() {
        return `
            <div class="p-6">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">التقارير والإحصائيات</h2>
                    <p class="text-gray-600">تحليل أداء المطعم والمبيعات</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Sales Chart -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">
                            <i class="fas fa-chart-line text-blue-500 mr-2"></i>
                            مبيعات الأسبوع
                        </h3>
                        <div class="h-64 flex items-end justify-between space-x-2">
                            ${this.generateWeeklySalesChart()}
                        </div>
                    </div>

                    <!-- Top Items -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">
                            <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                            الأصناف الأكثر مبيعاً
                        </h3>
                        <div class="space-y-3">
                            ${this.getTopSellingItems().map((item, index) => `
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <span class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
                                            ${index + 1}
                                        </span>
                                        <span class="font-semibold">${item.name}</span>
                                    </div>
                                    <span class="text-gray-600">${item.count} مرة</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <!-- Detailed Stats -->
                <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white rounded-lg shadow-md p-6 text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-calendar-day text-blue-600 text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">${this.getTodayStats().orders}</h3>
                        <p class="text-gray-600">طلبات اليوم</p>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6 text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-dollar-sign text-green-600 text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">${this.getTodayStats().revenue.toFixed(2)}</h3>
                        <p class="text-gray-600">مبيعات اليوم (${this.settings.currency})</p>
                    </div>

                    <div class="bg-white rounded-lg shadow-md p-6 text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-chart-bar text-purple-600 text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">${this.getAverageOrderValue().toFixed(2)}</h3>
                        <p class="text-gray-600">متوسط قيمة الطلب (${this.settings.currency})</p>
                    </div>
                </div>
            </div>
        `;
    }

    // Helper methods for the new pages
    getCustomerStats() {
        const customerMap = {};

        this.orders.forEach(order => {
            if (!customerMap[order.customerName]) {
                customerMap[order.customerName] = {
                    name: order.customerName,
                    phone: order.customerPhone,
                    orderCount: 0,
                    totalSpent: 0,
                    lastOrder: order.time
                };
            }

            customerMap[order.customerName].orderCount++;
            customerMap[order.customerName].totalSpent += order.total;
        });

        return Object.values(customerMap).sort((a, b) => b.totalSpent - a.totalSpent);
    }

    getNextStatusText(currentStatus) {
        const statusFlow = {
            'جديد': 'بدء التحضير',
            'جاري التحضير': 'جاهز للتسليم',
            'جاهز': 'تم التسليم'
        };
        return statusFlow[currentStatus] || 'تحديث الحالة';
    }

    updateOrderStatus(orderId, action) {
        const order = this.orders.find(o => o.id === orderId);
        if (!order) return;

        const statusFlow = {
            'جديد': 'جاري التحضير',
            'جاري التحضير': 'جاهز',
            'جاهز': 'تم التسليم'
        };

        if (action === 'next' && statusFlow[order.status]) {
            order.status = statusFlow[order.status];
            this.showNotification(`تم تحديث حالة الطلب #${orderId} إلى: ${order.status}`, 'success');

            // Refresh the orders page if it's currently shown
            if (this.currentPage === 'orders') {
                this.showPage('orders');
            }
        }
    }

    printOrder(orderId) {
        const order = this.orders.find(o => o.id === orderId);
        if (!order) return;

        // Create a simple print layout
        const printContent = `
            <div style="font-family: Arial, sans-serif; direction: rtl; text-align: right;">
                <h2 style="text-align: center;">${this.settings.restaurantName}</h2>
                <hr>
                <p><strong>رقم الطلب:</strong> ${order.id}</p>
                <p><strong>العميل:</strong> ${order.customerName}</p>
                <p><strong>الوقت:</strong> ${order.time}</p>
                <p><strong>نوع الطلب:</strong> ${order.type}</p>
                <hr>
                <h3>تفاصيل الطلب:</h3>
                ${order.items.map(item => `
                    <p>${item.name} × ${item.quantity} = ${(item.price * item.quantity).toFixed(2)} ${this.settings.currency}</p>
                `).join('')}
                <hr>
                <p><strong>الإجمالي: ${order.total.toFixed(2)} ${this.settings.currency}</strong></p>
            </div>
        `;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();

        this.showNotification('تم إرسال الطلب للطباعة', 'success');
    }

    generateWeeklySalesChart() {
        const days = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
        const salesData = [120, 150, 180, 90, 200, 250, 180]; // Sample data
        const maxSale = Math.max(...salesData);

        return days.map((day, index) => {
            const height = (salesData[index] / maxSale) * 200;
            return `
                <div class="flex flex-col items-center">
                    <div class="bg-orange-500 rounded-t" style="width: 30px; height: ${height}px; margin-bottom: 8px;"></div>
                    <span class="text-xs text-gray-600 transform -rotate-45">${day}</span>
                </div>
            `;
        }).join('');
    }

    getTopSellingItems() {
        const itemStats = {};
        this.orders.forEach(order => {
            order.items.forEach(item => {
                if (itemStats[item.name]) {
                    itemStats[item.name] += item.quantity;
                } else {
                    itemStats[item.name] = item.quantity;
                }
            });
        });

        return Object.entries(itemStats)
            .map(([name, count]) => ({ name, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5);
    }

    getTodayStats() {
        const today = new Date().toLocaleDateString('ar-SA');
        const todayOrders = this.orders.filter(order => order.date === today);

        return {
            orders: todayOrders.length,
            revenue: todayOrders.reduce((sum, order) => sum + order.total, 0)
        };
    }

    getAverageOrderValue() {
        if (this.orders.length === 0) return 0;
        const totalRevenue = this.orders.reduce((sum, order) => sum + order.total, 0);
        return totalRevenue / this.orders.length;
    }

    // Placeholder methods for other pages
    createCouponsPage() {
        return `
            <div class="p-6">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">إدارة الكوبونات</h2>
                    <p class="text-gray-600">إنشاء وإدارة كوبونات الخصم</p>
                </div>
                <div class="bg-white rounded-lg shadow-md p-8 text-center">
                    <i class="fas fa-tags text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">قريباً</h3>
                    <p class="text-gray-500">سيتم إضافة نظام إدارة الكوبونات قريباً</p>
                </div>
            </div>
        `;
    }

    createDeliveryPage() {
        return `
            <div class="p-6">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">إدارة التوصيل</h2>
                    <p class="text-gray-600">إعداد مناطق التوصيل والرسوم</p>
                </div>
                <div class="bg-white rounded-lg shadow-md p-8 text-center">
                    <i class="fas fa-truck text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">قريباً</h3>
                    <p class="text-gray-500">سيتم إضافة نظام إدارة التوصيل قريباً</p>
                </div>
            </div>
        `;
    }

    createSettingsPage() {
        return `
            <div class="p-6">
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">إعدادات النظام</h2>
                    <p class="text-gray-600">تخصيص إعدادات المطعم والنظام</p>
                </div>
                <div class="bg-white rounded-lg shadow-md p-8 text-center">
                    <i class="fas fa-cog text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">قريباً</h3>
                    <p class="text-gray-500">سيتم إضافة صفحة الإعدادات قريباً</p>
                </div>
            </div>
        `;
    }
}

// Initialize the system
const restaurantSystem = new RestaurantSystem();

// Make showPage function global for onclick handlers
window.showPage = (pageName) => {
    restaurantSystem.showPage(pageName);
};
