{"name": "restaurant-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@prisma/client": "^5.7.1", "prisma": "^5.7.1", "next-auth": "^4.24.5", "bcryptjs": "^2.4.3", "@types/bcryptjs": "^2.4.6", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "lucide-react": "^0.303.0", "react-to-print": "^2.14.15", "date-fns": "^3.0.6", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.0.4"}}