import { z } from "zod"

export const loginSchema = z.object({
  email: z.string().email("البريد الإلكتروني غير صحيح"),
  password: z.string().min(6, "كلمة المرور يجب أن تكون 6 أحرف على الأقل"),
})

export const menuItemSchema = z.object({
  name: z.string().min(1, "اسم الصنف مطلوب"),
  description: z.string().optional(),
  price: z.number().min(0, "السعر يجب أن يكون أكبر من صفر"),
  image: z.string().optional(),
  categoryId: z.string().min(1, "الفئة مطلوبة"),
  isActive: z.boolean().default(true),
  isAvailable: z.boolean().default(true),
  sortOrder: z.number().default(0),
})

export const categorySchema = z.object({
  name: z.string().min(1, "اسم الفئة مطلوب"),
  description: z.string().optional(),
  image: z.string().optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().default(0),
})

export const orderSchema = z.object({
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  customerEmail: z.string().email().optional().or(z.literal("")),
  orderType: z.enum(["dine-in", "takeaway", "delivery"]),
  notes: z.string().optional(),
  deliveryAddress: z.string().optional(),
  deliveryZoneId: z.string().optional(),
  couponCode: z.string().optional(),
  paymentMethod: z.enum(["cash", "card", "online"]).optional(),
  orderItems: z.array(z.object({
    menuItemId: z.string(),
    quantity: z.number().min(1),
    notes: z.string().optional(),
  })).min(1, "يجب إضافة صنف واحد على الأقل"),
})

export const deliveryZoneSchema = z.object({
  name: z.string().min(1, "اسم المنطقة مطلوب"),
  description: z.string().optional(),
  fee: z.number().min(0, "رسوم التوصيل يجب أن تكون أكبر من أو تساوي صفر"),
  minOrder: z.number().min(0, "الحد الأدنى للطلب يجب أن يكون أكبر من أو يساوي صفر"),
  isActive: z.boolean().default(true),
})

export const couponSchema = z.object({
  code: z.string().min(1, "كود الكوبون مطلوب").toUpperCase(),
  name: z.string().min(1, "اسم الكوبون مطلوب"),
  description: z.string().optional(),
  type: z.enum(["percentage", "fixed"]),
  value: z.number().min(0, "قيمة الخصم يجب أن تكون أكبر من صفر"),
  minOrder: z.number().min(0, "الحد الأدنى للطلب يجب أن يكون أكبر من أو يساوي صفر"),
  maxDiscount: z.number().min(0).optional(),
  usageLimit: z.number().min(1).optional(),
  isActive: z.boolean().default(true),
  validFrom: z.date(),
  validUntil: z.date(),
}).refine((data) => data.validUntil > data.validFrom, {
  message: "تاريخ انتهاء الصلاحية يجب أن يكون بعد تاريخ البداية",
  path: ["validUntil"],
}).refine((data) => {
  if (data.type === "percentage") {
    return data.value <= 100
  }
  return true
}, {
  message: "نسبة الخصم يجب أن تكون أقل من أو تساوي 100%",
  path: ["value"],
})

export const restaurantSettingsSchema = z.object({
  name: z.string().min(1, "اسم المطعم مطلوب"),
  description: z.string().optional(),
  logo: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email().optional().or(z.literal("")),
  website: z.string().url().optional().or(z.literal("")),
  facebook: z.string().optional(),
  instagram: z.string().optional(),
  twitter: z.string().optional(),
  whatsapp: z.string().optional(),
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, "لون غير صحيح"),
  secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, "لون غير صحيح"),
  accentColor: z.string().regex(/^#[0-9A-F]{6}$/i, "لون غير صحيح"),
  currency: z.string().min(1, "العملة مطلوبة"),
  taxRate: z.number().min(0).max(1, "معدل الضريبة يجب أن يكون بين 0 و 1"),
  deliveryEnabled: z.boolean().default(true),
  takeawayEnabled: z.boolean().default(true),
  dineInEnabled: z.boolean().default(true),
})
