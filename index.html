<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المطاعم</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .gradient-bg { background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); }
        .card-hover:hover { 
            transform: translateY(-4px); 
            transition: all 0.3s ease; 
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .hidden { display: none !important; }
        .modal-overlay {
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }
        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .btn-primary {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(249, 115, 22, 0.3);
        }
        .sidebar {
            background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
            box-shadow: 4px 0 20px rgba(0,0,0,0.1);
        }
        .menu-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            margin: 4px 0;
        }
        .menu-item:hover {
            background: rgba(249, 115, 22, 0.1);
            transform: translateX(-4px);
        }
        .menu-item.active {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            color: white;
        }
        .stats-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(249, 115, 22, 0.1);
        }
        .pos-item {
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .pos-item:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .cart-item {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 12px;
            border: 2px solid #f59e0b;
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 16px 24px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        .notification.show {
            transform: translateX(0);
        }
        .notification.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .notification.error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 modal-overlay flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md animate-fade-in">
            <div class="text-center mb-8">
                <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-utensils text-white text-2xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900">نظام إدارة المطاعم</h2>
                <p class="text-gray-600 mt-2">تسجيل الدخول إلى لوحة الإدارة</p>
            </div>

            <form id="loginForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" id="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500" placeholder="<EMAIL>" value="<EMAIL>">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                    <input type="password" id="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500" placeholder="••••••••" value="admin123">
                </div>

                <button type="submit" class="w-full btn-primary text-white py-3 rounded-lg font-semibold">
                    تسجيل الدخول
                </button>
            </form>

            <div class="mt-6 text-center text-sm text-gray-600">
                <p>البيانات الافتراضية:</p>
                <p class="font-mono"><EMAIL> / admin123</p>
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div id="mainApp">
        <!-- Header -->
        <header class="gradient-bg text-white shadow-lg sticky top-0 z-40">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <button id="sidebarToggle" class="lg:hidden text-white hover:bg-white/20 p-2 rounded-lg">
                            <i class="fas fa-bars"></i>
                        </button>
                        <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                            <i class="fas fa-utensils text-orange-500"></i>
                        </div>
                        <h1 class="text-xl font-bold">مطعم الذواقة</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div id="userInfo" class="hidden md:flex items-center space-x-2 bg-white/20 px-3 py-1 rounded-full hidden">
                            <i class="fas fa-user text-sm"></i>
                            <span class="text-sm">مدير النظام</span>
                        </div>
                        <button id="loginBtn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-all">
                            تسجيل الدخول للإدارة
                        </button>
                        <button id="logoutBtn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-all hidden">
                            تسجيل الخروج
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- Sidebar -->
            <aside id="sidebar" class="sidebar w-64 min-h-screen text-white transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out fixed lg:relative z-30">
                <div class="p-6">
                    <nav class="space-y-2">
                        <a href="#" class="menu-item active flex items-center space-x-3 px-4 py-3" data-page="menu">
                            <i class="fas fa-utensils w-5"></i>
                            <span>القائمة</span>
                        </a>
                        <a href="#" class="menu-item flex items-center space-x-3 px-4 py-3" data-page="pos">
                            <i class="fas fa-cash-register w-5"></i>
                            <span>نقطة البيع</span>
                        </a>

                        <!-- Admin Section -->
                        <div id="adminSection" class="hidden">
                            <hr class="my-4 border-gray-600">
                            <p class="text-xs text-gray-400 px-4 mb-2">الإدارة</p>
                            <a href="#" class="menu-item flex items-center space-x-3 px-4 py-3" data-page="dashboard">
                                <i class="fas fa-tachometer-alt w-5"></i>
                                <span>لوحة التحكم</span>
                            </a>
                            <a href="#" class="menu-item flex items-center space-x-3 px-4 py-3" data-page="orders">
                                <i class="fas fa-shopping-cart w-5"></i>
                                <span>الطلبات</span>
                            </a>
                            <a href="#" class="menu-item flex items-center space-x-3 px-4 py-3" data-page="customers">
                                <i class="fas fa-users w-5"></i>
                                <span>العملاء</span>
                            </a>
                            <a href="#" class="menu-item flex items-center space-x-3 px-4 py-3" data-page="reports">
                                <i class="fas fa-chart-bar w-5"></i>
                                <span>التقارير</span>
                            </a>
                            <a href="#" class="menu-item flex items-center space-x-3 px-4 py-3" data-page="coupons">
                                <i class="fas fa-tags w-5"></i>
                                <span>الكوبونات</span>
                            </a>
                            <a href="#" class="menu-item flex items-center space-x-3 px-4 py-3" data-page="delivery">
                                <i class="fas fa-truck w-5"></i>
                                <span>التوصيل</span>
                            </a>
                            <a href="#" class="menu-item flex items-center space-x-3 px-4 py-3" data-page="settings">
                                <i class="fas fa-cog w-5"></i>
                                <span>الإعدادات</span>
                            </a>
                        </div>
                    </nav>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 lg:mr-64">
                <!-- Menu Page (Default) -->
                <div id="menuPage" class="page-content p-6">
                    <div class="mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">قائمة مطعم الذواقة</h2>
                        <p class="text-gray-600">اكتشف أشهى الأطباق والمأكولات الشرقية والغربية</p>
                    </div>

                    <!-- Category Filter -->
                    <div class="flex flex-wrap gap-2 mb-8 justify-center">
                        <button class="category-filter-btn active px-6 py-3 rounded-full bg-orange-500 text-white font-semibold" data-category="all">
                            جميع الأصناف
                        </button>
                        <button class="category-filter-btn px-6 py-3 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 font-semibold" data-category="أطباق رئيسية">
                            أطباق رئيسية
                        </button>
                        <button class="category-filter-btn px-6 py-3 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 font-semibold" data-category="مقبلات">
                            مقبلات
                        </button>
                        <button class="category-filter-btn px-6 py-3 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 font-semibold" data-category="مشروبات">
                            مشروبات
                        </button>
                        <button class="category-filter-btn px-6 py-3 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 font-semibold" data-category="حلويات">
                            حلويات
                        </button>
                    </div>

                    <!-- Menu Items Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="publicMenuGrid">
                        <!-- Menu items will be populated by JavaScript -->
                    </div>

                    <!-- Restaurant Info -->
                    <div class="mt-12 bg-gradient-to-r from-orange-50 to-orange-100 rounded-2xl p-8">
                        <div class="text-center">
                            <h3 class="text-2xl font-bold text-gray-900 mb-4">مطعم الذواقة</h3>
                            <p class="text-gray-700 mb-6">نقدم لكم أشهى الأطباق الشرقية والغربية بأجود المكونات وأفضل الأسعار</p>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                                        <i class="fas fa-utensils text-2xl"></i>
                                    </div>
                                    <h4 class="font-semibold mb-2">أطباق شهية</h4>
                                    <p class="text-sm text-gray-600">مجموعة متنوعة من الأطباق اللذيذة</p>
                                </div>
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                                        <i class="fas fa-truck text-2xl"></i>
                                    </div>
                                    <h4 class="font-semibold mb-2">توصيل سريع</h4>
                                    <p class="text-sm text-gray-600">خدمة توصيل سريعة لجميع المناطق</p>
                                </div>
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                                        <i class="fas fa-star text-2xl"></i>
                                    </div>
                                    <h4 class="font-semibold mb-2">جودة عالية</h4>
                                    <p class="text-sm text-gray-600">أفضل المكونات وأعلى معايير الجودة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Page (Hidden by default) -->
                <div id="dashboardPage" class="page-content p-6 hidden">
                    <div class="mb-8">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">لوحة التحكم الرئيسية</h2>
                        <p class="text-gray-600">نظرة عامة على أداء المطعم</p>
                    </div>

                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="stats-card p-6 card-hover">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                                    <p class="text-3xl font-bold text-gray-900" id="totalOrders">156</p>
                                    <p class="text-sm text-green-600 mt-1">
                                        <i class="fas fa-arrow-up"></i> +12% من الشهر الماضي
                                    </p>
                                </div>
                                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-shopping-cart text-blue-600 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card p-6 card-hover">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                                    <p class="text-3xl font-bold text-gray-900" id="totalRevenue">24,580 ر.س</p>
                                    <p class="text-sm text-green-600 mt-1">
                                        <i class="fas fa-arrow-up"></i> +8% من الشهر الماضي
                                    </p>
                                </div>
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-dollar-sign text-green-600 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card p-6 card-hover">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">طلبات اليوم</p>
                                    <p class="text-3xl font-bold text-gray-900" id="todayOrders">23</p>
                                    <p class="text-sm text-orange-600 mt-1">
                                        <i class="fas fa-clock"></i> آخر طلب منذ 5 دقائق
                                    </p>
                                </div>
                                <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-chart-line text-orange-600 text-2xl"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card p-6 card-hover">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">مبيعات اليوم</p>
                                    <p class="text-3xl font-bold text-gray-900" id="todayRevenue">1,850 ر.س</p>
                                    <p class="text-sm text-purple-600 mt-1">
                                        <i class="fas fa-target"></i> 75% من الهدف اليومي
                                    </p>
                                </div>
                                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-chart-pie text-purple-600 text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <button class="stats-card p-6 card-hover text-center hover:shadow-lg" onclick="showPage('pos')">
                            <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-cash-register text-white text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">طلب جديد</h3>
                            <p class="text-sm text-gray-600">إنشاء طلب من نقطة البيع</p>
                        </button>

                        <button class="stats-card p-6 card-hover text-center hover:shadow-lg" onclick="showPage('menu')">
                            <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-utensils text-white text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">إدارة القائمة</h3>
                            <p class="text-sm text-gray-600">إضافة وتعديل الأصناف</p>
                        </button>

                        <button class="stats-card p-6 card-hover text-center hover:shadow-lg" onclick="showPage('orders')">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-list-alt text-white text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">الطلبات الحالية</h3>
                            <p class="text-sm text-gray-600">متابعة الطلبات الجارية</p>
                        </button>

                        <button class="stats-card p-6 card-hover text-center hover:shadow-lg" onclick="showPage('reports')">
                            <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-chart-bar text-white text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">التقارير</h3>
                            <p class="text-sm text-gray-600">إحصائيات المبيعات</p>
                        </button>
                    </div>

                    <!-- Recent Orders -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div class="stats-card p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">
                                <i class="fas fa-clock text-orange-500 mr-2"></i>
                                الطلبات الأخيرة
                            </h3>
                            <div class="space-y-4" id="recentOrders">
                                <!-- Recent orders will be populated by JavaScript -->
                            </div>
                        </div>

                        <div class="stats-card p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">
                                <i class="fas fa-star text-yellow-500 mr-2"></i>
                                الأصناف الأكثر مبيعاً
                            </h3>
                            <div class="space-y-4" id="topItems">
                                <!-- Top items will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <i class="fas fa-check-circle mr-2"></i>
        <span id="notificationText"></span>
    </div>

    <script src="app.js"></script>
</body>
</html>
