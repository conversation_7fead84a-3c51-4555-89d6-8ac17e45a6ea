import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { code } = await request.json()

    if (!code) {
      return NextResponse.json(
        { success: false, error: 'كود الكوبون مطلوب' },
        { status: 400 }
      )
    }

    const coupon = await prisma.coupon.findUnique({
      where: { code: code.toUpperCase() }
    })

    if (!coupon) {
      return NextResponse.json(
        { success: false, error: 'كوبون غير موجود' },
        { status: 404 }
      )
    }

    if (!coupon.isActive) {
      return NextResponse.json(
        { success: false, error: 'كوبون غير نشط' },
        { status: 400 }
      )
    }

    const now = new Date()
    if (now < coupon.validFrom) {
      return NextResponse.json(
        { success: false, error: 'كوبون لم يبدأ بعد' },
        { status: 400 }
      )
    }

    if (now > coupon.validUntil) {
      return NextResponse.json(
        { success: false, error: 'كوبون منتهي الصلاحية' },
        { status: 400 }
      )
    }

    if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) {
      return NextResponse.json(
        { success: false, error: 'تم استنفاد استخدامات الكوبون' },
        { status: 400 }
      )
    }

    return NextResponse.json({ success: true, coupon })
  } catch (error) {
    console.error('Error validating coupon:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في التحقق من الكوبون' },
      { status: 500 }
    )
  }
}
