'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useRestaurant } from '../providers'
import { formatCurrency, calculateDiscount } from '@/lib/utils'
import { Category, MenuItem, CartItem, DeliveryZone, Coupon } from '@/types'
import { 
  ArrowRight, 
  Plus, 
  Minus, 
  Trash2, 
  ShoppingCart,
  User,
  Phone,
  MapPin,
  Tag,
  CreditCard,
  Printer
} from 'lucide-react'
import Link from 'next/link'

export default function POSPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [cart, setCart] = useState<CartItem[]>([])
  const [deliveryZones, setDeliveryZones] = useState<DeliveryZone[]>([])
  const [loading, setLoading] = useState(true)
  const [orderType, setOrderType] = useState<'dine-in' | 'takeaway' | 'delivery'>('dine-in')
  
  // Customer info
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [deliveryAddress, setDeliveryAddress] = useState('')
  const [selectedDeliveryZone, setSelectedDeliveryZone] = useState<string>('')
  const [couponCode, setCouponCode] = useState('')
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null)
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card'>('cash')
  
  const { settings } = useRestaurant()

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [categoriesRes, menuItemsRes, deliveryZonesRes] = await Promise.all([
        fetch('/api/categories'),
        fetch('/api/menu'),
        fetch('/api/delivery-zones')
      ])

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json()
        setCategories(categoriesData.categories || [])
      }

      if (menuItemsRes.ok) {
        const menuData = await menuItemsRes.json()
        setMenuItems(menuData.menuItems || [])
      }

      if (deliveryZonesRes.ok) {
        const deliveryData = await deliveryZonesRes.json()
        setDeliveryZones(deliveryData.deliveryZones || [])
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const addToCart = (menuItem: MenuItem) => {
    const existingItem = cart.find(item => item.menuItem.id === menuItem.id)
    if (existingItem) {
      setCart(cart.map(item =>
        item.menuItem.id === menuItem.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ))
    } else {
      setCart([...cart, { menuItem, quantity: 1 }])
    }
  }

  const updateQuantity = (menuItemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(menuItemId)
    } else {
      setCart(cart.map(item =>
        item.menuItem.id === menuItemId
          ? { ...item, quantity }
          : item
      ))
    }
  }

  const removeFromCart = (menuItemId: string) => {
    setCart(cart.filter(item => item.menuItem.id !== menuItemId))
  }

  const applyCoupon = async () => {
    if (!couponCode.trim()) return

    try {
      const response = await fetch(`/api/coupons/validate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: couponCode.toUpperCase() })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setAppliedCoupon(data.coupon)
        } else {
          alert(data.error || 'كوبون غير صحيح')
        }
      }
    } catch (error) {
      console.error('Error applying coupon:', error)
      alert('حدث خطأ في تطبيق الكوبون')
    }
  }

  const removeCoupon = () => {
    setAppliedCoupon(null)
    setCouponCode('')
  }

  const filteredItems = selectedCategory === 'all' 
    ? menuItems.filter(item => item.isActive && item.isAvailable)
    : menuItems.filter(item => 
        item.categoryId === selectedCategory && 
        item.isActive && 
        item.isAvailable
      )

  // Calculate totals
  const subtotal = cart.reduce((sum, item) => sum + (item.menuItem.price * item.quantity), 0)
  const deliveryFee = orderType === 'delivery' && selectedDeliveryZone 
    ? deliveryZones.find(zone => zone.id === selectedDeliveryZone)?.fee || 0 
    : 0
  const discount = appliedCoupon ? calculateDiscount(subtotal, appliedCoupon) : 0
  const taxAmount = (subtotal - discount + deliveryFee) * (settings?.taxRate || 0.15)
  const total = subtotal - discount + deliveryFee + taxAmount

  const createOrder = async () => {
    if (cart.length === 0) {
      alert('يرجى إضافة أصناف إلى السلة')
      return
    }

    if (orderType === 'delivery' && !selectedDeliveryZone) {
      alert('يرجى اختيار منطقة التوصيل')
      return
    }

    try {
      const orderData = {
        customerName: customerName || undefined,
        customerPhone: customerPhone || undefined,
        orderType,
        deliveryAddress: orderType === 'delivery' ? deliveryAddress : undefined,
        deliveryZoneId: orderType === 'delivery' ? selectedDeliveryZone : undefined,
        couponCode: appliedCoupon?.code,
        paymentMethod,
        orderItems: cart.map(item => ({
          menuItemId: item.menuItem.id,
          quantity: item.quantity,
          notes: item.notes
        }))
      }

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orderData)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          alert('تم إنشاء الطلب بنجاح!')
          // Reset form
          setCart([])
          setCustomerName('')
          setCustomerPhone('')
          setDeliveryAddress('')
          setSelectedDeliveryZone('')
          setCouponCode('')
          setAppliedCoupon(null)
          // Optionally redirect to order details or print
        } else {
          alert(data.error || 'حدث خطأ في إنشاء الطلب')
        }
      }
    } catch (error) {
      console.error('Error creating order:', error)
      alert('حدث خطأ في إنشاء الطلب')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowRight className="h-4 w-4 mr-2" />
                  العودة
                </Button>
              </Link>
              <h1 className="text-xl font-bold text-gray-900">نقطة البيع</h1>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">
                <ShoppingCart className="h-3 w-3 mr-1" />
                {cart.reduce((sum, item) => sum + item.quantity, 0)} صنف
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Menu Section */}
          <div className="lg:col-span-2">
            {/* Order Type Selection */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">نوع الطلب</h3>
              <div className="flex space-x-2">
                <Button
                  variant={orderType === 'dine-in' ? 'default' : 'outline'}
                  onClick={() => setOrderType('dine-in')}
                  size="sm"
                >
                  تناول في المطعم
                </Button>
                <Button
                  variant={orderType === 'takeaway' ? 'default' : 'outline'}
                  onClick={() => setOrderType('takeaway')}
                  size="sm"
                >
                  استلام
                </Button>
                <Button
                  variant={orderType === 'delivery' ? 'default' : 'outline'}
                  onClick={() => setOrderType('delivery')}
                  size="sm"
                >
                  توصيل
                </Button>
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2 mb-6">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                onClick={() => setSelectedCategory('all')}
                size="sm"
              >
                جميع الأصناف
              </Button>
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory(category.id)}
                  size="sm"
                >
                  {category.name}
                </Button>
              ))}
            </div>

            {/* Menu Items Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filteredItems.map((item) => (
                <Card key={item.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-base">{item.name}</CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        {formatCurrency(item.price, settings?.currency)}
                      </Badge>
                    </div>
                    {item.description && (
                      <CardDescription className="text-xs">
                        {item.description}
                      </CardDescription>
                    )}
                  </CardHeader>
                  <CardContent className="pt-0">
                    <Button
                      onClick={() => addToCart(item)}
                      className="w-full"
                      size="sm"
                      disabled={!item.isAvailable}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      إضافة
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Cart Section */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  السلة
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Customer Info */}
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium flex items-center mb-1">
                      <User className="h-3 w-3 mr-1" />
                      اسم العميل
                    </label>
                    <Input
                      value={customerName}
                      onChange={(e) => setCustomerName(e.target.value)}
                      placeholder="اختياري"
                      size="sm"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium flex items-center mb-1">
                      <Phone className="h-3 w-3 mr-1" />
                      رقم الهاتف
                    </label>
                    <Input
                      value={customerPhone}
                      onChange={(e) => setCustomerPhone(e.target.value)}
                      placeholder="اختياري"
                      size="sm"
                    />
                  </div>
                  
                  {orderType === 'delivery' && (
                    <>
                      <div>
                        <label className="text-sm font-medium flex items-center mb-1">
                          <MapPin className="h-3 w-3 mr-1" />
                          عنوان التوصيل
                        </label>
                        <Input
                          value={deliveryAddress}
                          onChange={(e) => setDeliveryAddress(e.target.value)}
                          placeholder="العنوان الكامل"
                          size="sm"
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-1">منطقة التوصيل</label>
                        <select
                          value={selectedDeliveryZone}
                          onChange={(e) => setSelectedDeliveryZone(e.target.value)}
                          className="w-full p-2 border rounded-md text-sm"
                        >
                          <option value="">اختر المنطقة</option>
                          {deliveryZones.map((zone) => (
                            <option key={zone.id} value={zone.id}>
                              {zone.name} - {formatCurrency(zone.fee, settings?.currency)}
                            </option>
                          ))}
                        </select>
                      </div>
                    </>
                  )}
                </div>

                {/* Cart Items */}
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {cart.length === 0 ? (
                    <p className="text-sm text-gray-500 text-center py-4">
                      السلة فارغة
                    </p>
                  ) : (
                    cart.map((item) => (
                      <div key={item.menuItem.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex-1">
                          <p className="text-sm font-medium">{item.menuItem.name}</p>
                          <p className="text-xs text-gray-500">
                            {formatCurrency(item.menuItem.price, settings?.currency)}
                          </p>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.menuItem.id, item.quantity - 1)}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="text-sm font-medium px-2">{item.quantity}</span>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.menuItem.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => removeFromCart(item.menuItem.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* Coupon */}
                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center">
                    <Tag className="h-3 w-3 mr-1" />
                    كوبون الخصم
                  </label>
                  {appliedCoupon ? (
                    <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                      <span className="text-sm text-green-700">{appliedCoupon.code}</span>
                      <Button size="sm" variant="outline" onClick={removeCoupon}>
                        إزالة
                      </Button>
                    </div>
                  ) : (
                    <div className="flex space-x-1">
                      <Input
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                        placeholder="كود الكوبون"
                        size="sm"
                      />
                      <Button size="sm" onClick={applyCoupon}>
                        تطبيق
                      </Button>
                    </div>
                  )}
                </div>

                {/* Payment Method */}
                <div>
                  <label className="text-sm font-medium flex items-center mb-2">
                    <CreditCard className="h-3 w-3 mr-1" />
                    طريقة الدفع
                  </label>
                  <div className="flex space-x-2">
                    <Button
                      variant={paymentMethod === 'cash' ? 'default' : 'outline'}
                      onClick={() => setPaymentMethod('cash')}
                      size="sm"
                    >
                      نقدي
                    </Button>
                    <Button
                      variant={paymentMethod === 'card' ? 'default' : 'outline'}
                      onClick={() => setPaymentMethod('card')}
                      size="sm"
                    >
                      بطاقة
                    </Button>
                  </div>
                </div>

                {/* Order Summary */}
                {cart.length > 0 && (
                  <div className="border-t pt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>المجموع الفرعي:</span>
                      <span>{formatCurrency(subtotal, settings?.currency)}</span>
                    </div>
                    {discount > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>الخصم:</span>
                        <span>-{formatCurrency(discount, settings?.currency)}</span>
                      </div>
                    )}
                    {deliveryFee > 0 && (
                      <div className="flex justify-between text-sm">
                        <span>رسوم التوصيل:</span>
                        <span>{formatCurrency(deliveryFee, settings?.currency)}</span>
                      </div>
                    )}
                    <div className="flex justify-between text-sm">
                      <span>الضريبة ({((settings?.taxRate || 0.15) * 100).toFixed(0)}%):</span>
                      <span>{formatCurrency(taxAmount, settings?.currency)}</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg border-t pt-2">
                      <span>الإجمالي:</span>
                      <span>{formatCurrency(total, settings?.currency)}</span>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-2">
                  <Button
                    onClick={createOrder}
                    className="w-full"
                    disabled={cart.length === 0}
                  >
                    <Printer className="h-4 w-4 mr-2" />
                    إنشاء الطلب
                  </Button>
                  {cart.length > 0 && (
                    <Button
                      variant="outline"
                      onClick={() => setCart([])}
                      className="w-full"
                    >
                      مسح السلة
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
