const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('Setting up database...')

  // Create default admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  try {
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'مدير النظام',
        role: 'admin',
      },
    })
    console.log('Admin user created:', user.email)
  } catch (error) {
    console.log('Admin user already exists or error:', error.message)
  }

  // Create default categories
  const categories = [
    { name: 'المقبلات', description: 'مقبلات شهية ومتنوعة', sortOrder: 1 },
    { name: 'الأطباق الرئيسية', description: 'أطباق رئيسية لذيذة', sortOrder: 2 },
    { name: 'المشروبات', description: 'مشروبات باردة وساخنة', sortOrder: 3 },
    { name: 'الحلويات', description: 'حلويات شرقية وغربية', sortOrder: 4 },
  ]

  for (const category of categories) {
    try {
      await prisma.category.upsert({
        where: { name: category.name },
        update: {},
        create: category,
      })
      console.log('Category created:', category.name)
    } catch (error) {
      console.log('Category already exists:', category.name)
    }
  }

  // Create sample menu items
  const appetizersCategory = await prisma.category.findFirst({
    where: { name: 'المقبلات' }
  })

  const mainCategory = await prisma.category.findFirst({
    where: { name: 'الأطباق الرئيسية' }
  })

  const drinksCategory = await prisma.category.findFirst({
    where: { name: 'المشروبات' }
  })

  const dessertsCategory = await prisma.category.findFirst({
    where: { name: 'الحلويات' }
  })

  const menuItems = [
    // Appetizers
    {
      name: 'حمص بالطحينة',
      description: 'حمص كريمي مع الطحينة وزيت الزيتون',
      price: 15.00,
      categoryId: appetizersCategory?.id,
      sortOrder: 1,
    },
    {
      name: 'متبل باذنجان',
      description: 'متبل باذنجان مشوي مع الثوم والليمون',
      price: 18.00,
      categoryId: appetizersCategory?.id,
      sortOrder: 2,
    },
    // Main dishes
    {
      name: 'كبسة لحم',
      description: 'أرز بسمتي مع لحم الخروف والخضار',
      price: 45.00,
      categoryId: mainCategory?.id,
      sortOrder: 1,
    },
    {
      name: 'مندي دجاج',
      description: 'دجاج مشوي مع أرز مندي بالتوابل',
      price: 35.00,
      categoryId: mainCategory?.id,
      sortOrder: 2,
    },
    // Drinks
    {
      name: 'عصير برتقال طازج',
      description: 'عصير برتقال طبيعي 100%',
      price: 12.00,
      categoryId: drinksCategory?.id,
      sortOrder: 1,
    },
    {
      name: 'شاي أحمر',
      description: 'شاي أحمر مع النعناع',
      price: 8.00,
      categoryId: drinksCategory?.id,
      sortOrder: 2,
    },
    // Desserts
    {
      name: 'كنافة نابلسية',
      description: 'كنافة بالجبن والقطر',
      price: 25.00,
      categoryId: dessertsCategory?.id,
      sortOrder: 1,
    },
    {
      name: 'بقلاوة',
      description: 'بقلاوة بالفستق والعسل',
      price: 20.00,
      categoryId: dessertsCategory?.id,
      sortOrder: 2,
    },
  ]

  for (const item of menuItems) {
    if (item.categoryId) {
      try {
        await prisma.menuItem.upsert({
          where: { name: item.name },
          update: {},
          create: item,
        })
        console.log('Menu item created:', item.name)
      } catch (error) {
        console.log('Menu item already exists:', item.name)
      }
    }
  }

  // Create default delivery zones
  const deliveryZones = [
    { name: 'المنطقة الأولى', description: 'وسط المدينة', fee: 10.00, minOrder: 50.00 },
    { name: 'المنطقة الثانية', description: 'الأحياء القريبة', fee: 15.00, minOrder: 75.00 },
    { name: 'المنطقة الثالثة', description: 'الأحياء البعيدة', fee: 25.00, minOrder: 100.00 },
  ]

  for (const zone of deliveryZones) {
    try {
      await prisma.deliveryZone.upsert({
        where: { name: zone.name },
        update: {},
        create: zone,
      })
      console.log('Delivery zone created:', zone.name)
    } catch (error) {
      console.log('Delivery zone already exists:', zone.name)
    }
  }

  // Create sample coupons
  const coupons = [
    {
      code: 'WELCOME10',
      name: 'خصم الترحيب',
      description: 'خصم 10% للعملاء الجدد',
      type: 'percentage',
      value: 10,
      minOrder: 50.00,
      maxDiscount: 20.00,
      usageLimit: 100,
      validFrom: new Date(),
      validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    },
    {
      code: 'SAVE20',
      name: 'وفر 20 ريال',
      description: 'خصم 20 ريال على الطلبات أكثر من 100 ريال',
      type: 'fixed',
      value: 20,
      minOrder: 100.00,
      usageLimit: 50,
      validFrom: new Date(),
      validUntil: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days
    },
  ]

  for (const coupon of coupons) {
    try {
      await prisma.coupon.upsert({
        where: { code: coupon.code },
        update: {},
        create: coupon,
      })
      console.log('Coupon created:', coupon.code)
    } catch (error) {
      console.log('Coupon already exists:', coupon.code)
    }
  }

  console.log('Database setup completed!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
