'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useRestaurant } from './providers'
import { 
  ShoppingCart, 
  Users, 
  Settings, 
  BarChart3, 
  Menu,
  Truck,
  Gift,
  Facebook,
  Instagram,
  Twitter
} from 'lucide-react'
import Link from 'next/link'

export default function HomePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { settings } = useRestaurant()

  useEffect(() => {
    if (status === 'loading') return
    if (!session) {
      router.push('/login')
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              {settings?.logo && (
                <img src={settings.logo} alt={settings.name} className="h-8 w-8" />
              )}
              <h1 className="text-xl font-bold text-gray-900">
                {settings?.name || 'نظام إدارة المطاعم'}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">مرحباً، {session.user?.name}</span>
              <Button 
                variant="outline" 
                onClick={() => router.push('/api/auth/signout')}
              >
                تسجيل الخروج
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">لوحة التحكم الرئيسية</h2>
          <p className="text-gray-600">إدارة شاملة لمطعمك من مكان واحد</p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Link href="/pos">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">نقطة البيع</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">POS</div>
                <p className="text-xs text-muted-foreground">
                  إنشاء طلبات جديدة
                </p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/menu">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">القائمة</CardTitle>
                <Menu className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">القائمة</div>
                <p className="text-xs text-muted-foreground">
                  عرض قائمة الطعام
                </p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/admin">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">الإدارة</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">الإدارة</div>
                <p className="text-xs text-muted-foreground">
                  إدارة النظام
                </p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/admin/orders">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">الطلبات</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">الطلبات</div>
                <p className="text-xs text-muted-foreground">
                  متابعة الطلبات
                </p>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Management Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle>إدارة المطعم</CardTitle>
              <CardDescription>إدارة جميع جوانب مطعمك</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Link href="/admin/menu">
                <Button variant="outline" className="w-full justify-start">
                  <Menu className="mr-2 h-4 w-4" />
                  إدارة القائمة
                </Button>
              </Link>
              <Link href="/admin/delivery-zones">
                <Button variant="outline" className="w-full justify-start">
                  <Truck className="mr-2 h-4 w-4" />
                  مناطق التوصيل
                </Button>
              </Link>
              <Link href="/admin/coupons">
                <Button variant="outline" className="w-full justify-start">
                  <Gift className="mr-2 h-4 w-4" />
                  إدارة الكوبونات
                </Button>
              </Link>
              <Link href="/admin/settings">
                <Button variant="outline" className="w-full justify-start">
                  <Settings className="mr-2 h-4 w-4" />
                  إعدادات المطعم
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>وسائل التواصل الاجتماعي</CardTitle>
              <CardDescription>تواصل مع عملائك</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {settings?.facebook && (
                <a href={settings.facebook} target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" className="w-full justify-start">
                    <Facebook className="mr-2 h-4 w-4" />
                    فيسبوك
                  </Button>
                </a>
              )}
              {settings?.instagram && (
                <a href={settings.instagram} target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" className="w-full justify-start">
                    <Instagram className="mr-2 h-4 w-4" />
                    إنستغرام
                  </Button>
                </a>
              )}
              {settings?.twitter && (
                <a href={settings.twitter} target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" className="w-full justify-start">
                    <Twitter className="mr-2 h-4 w-4" />
                    تويتر
                  </Button>
                </a>
              )}
              {(!settings?.facebook && !settings?.instagram && !settings?.twitter) && (
                <p className="text-sm text-muted-foreground">
                  لم يتم إعداد وسائل التواصل الاجتماعي بعد. يمكنك إضافتها من إعدادات المطعم.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
