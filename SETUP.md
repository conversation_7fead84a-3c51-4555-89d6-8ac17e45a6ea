# دليل التثبيت والتشغيل السريع

## المتطلبات الأساسية

قبل البدء، تأكد من تثبيت:
- Node.js (الإصدار 18 أو أحدث)
- npm (يأتي مع Node.js)

## خطوات التثبيت

### 1. تثبيت Node.js
إذا لم يكن Node.js مثبتاً على جهازك:
- قم بزيارة [nodejs.org](https://nodejs.org)
- حمل وثبت أحدث إصدار LTS

### 2. تثبيت المتطلبات
افتح Terminal/Command Prompt في مجلد المشروع وشغل:
```bash
npm install
```

### 3. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
npx prisma db push

# إنشاء البيانات الأولية (المستخدم الافتراضي والبيانات التجريبية)
node scripts/setup.js
```

### 4. تشغيل التطبيق
```bash
npm run dev
```

### 5. فتح التطبيق
افتح المتصفح واذهب إلى: `http://localhost:3000`

## بيانات تسجيل الدخول

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123

## استكشاف الأخطاء

### مشكلة: "npm is not recognized"
- تأكد من تثبيت Node.js بشكل صحيح
- أعد تشغيل Terminal/Command Prompt

### مشكلة: "prisma command not found"
```bash
npm install -g prisma
```

### مشكلة: خطأ في قاعدة البيانات
```bash
# احذف قاعدة البيانات وأعد إنشاؤها
rm prisma/dev.db
npx prisma db push
node scripts/setup.js
```

### مشكلة: المنفذ 3000 مستخدم
```bash
# استخدم منفذ آخر
npm run dev -- -p 3001
```

## الخطوات التالية

بعد تشغيل النظام بنجاح:

1. **سجل دخولك** باستخدام البيانات الافتراضية
2. **اذهب إلى الإعدادات** وحدث معلومات مطعمك
3. **أضف فئات وأصناف جديدة** من لوحة الإدارة
4. **جرب نقطة البيع** لإنشاء طلب تجريبي
5. **استكشف جميع الميزات** المتاحة

## الدعم

إذا واجهت أي مشاكل:
1. تأكد من اتباع جميع الخطوات بالترتيب
2. تحقق من رسائل الخطأ في Terminal
3. تأكد من أن جميع المتطلبات مثبتة بشكل صحيح

---

**نصيحة**: احتفظ بنسخة احتياطية من ملف `prisma/dev.db` بعد إعداد البيانات الأولية.
