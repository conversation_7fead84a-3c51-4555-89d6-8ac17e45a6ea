'use client'

import { SessionProvider } from 'next-auth/react'
import { createContext, useContext, useEffect, useState } from 'react'
import { RestaurantSettings } from '@/types'

interface RestaurantContextType {
  settings: RestaurantSettings | null
  updateSettings: (settings: RestaurantSettings) => void
}

const RestaurantContext = createContext<RestaurantContextType>({
  settings: null,
  updateSettings: () => {},
})

export const useRestaurant = () => useContext(RestaurantContext)

export function Providers({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<RestaurantSettings | null>(null)

  useEffect(() => {
    // Load restaurant settings
    fetch('/api/settings')
      .then(res => res.json())
      .then(data => {
        if (data.success) {
          setSettings(data.settings)
          // Apply theme colors
          if (data.settings) {
            document.documentElement.style.setProperty('--primary', `${hexToHsl(data.settings.primaryColor)}`)
            document.documentElement.style.setProperty('--secondary', `${hexToHsl(data.settings.secondaryColor)}`)
            document.documentElement.style.setProperty('--accent', `${hexToHsl(data.settings.accentColor)}`)
          }
        }
      })
      .catch(console.error)
  }, [])

  const updateSettings = (newSettings: RestaurantSettings) => {
    setSettings(newSettings)
    // Apply theme colors
    document.documentElement.style.setProperty('--primary', `${hexToHsl(newSettings.primaryColor)}`)
    document.documentElement.style.setProperty('--secondary', `${hexToHsl(newSettings.secondaryColor)}`)
    document.documentElement.style.setProperty('--accent', `${hexToHsl(newSettings.accentColor)}`)
  }

  return (
    <SessionProvider>
      <RestaurantContext.Provider value={{ settings, updateSettings }}>
        {children}
      </RestaurantContext.Provider>
    </SessionProvider>
  )
}

function hexToHsl(hex: string): string {
  // Convert hex to RGB
  const r = parseInt(hex.slice(1, 3), 16) / 255
  const g = parseInt(hex.slice(3, 5), 16) / 255
  const b = parseInt(hex.slice(5, 7), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0
  let s = 0
  const l = (max + min) / 2

  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }

  return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`
}
