<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المطاعم - معاينة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                        <span class="text-orange-500 font-bold">🍽️</span>
                    </div>
                    <h1 class="text-xl font-bold">نظام إدارة المطاعم</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm">مرحباً، مدير النظام</span>
                    <button class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-md text-sm">
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section -->
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">مرحباً بك في نظام إدارة المطاعم</h2>
            <p class="text-gray-600">نظام شامل لإدارة مطعمك بكفاءة واحترافية</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-blue-600 text-xl">🛒</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                        <p class="text-2xl font-bold text-gray-900">12,450 ر.س</p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                        <span class="text-green-600 text-xl">💰</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">طلبات اليوم</p>
                        <p class="text-2xl font-bold text-gray-900">23</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                        <span class="text-orange-600 text-xl">📈</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">مبيعات اليوم</p>
                        <p class="text-2xl font-bold text-gray-900">1,850 ر.س</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                        <span class="text-purple-600 text-xl">📊</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <button class="bg-white rounded-lg shadow-md p-6 card-hover text-center hover:shadow-lg">
                <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-orange-600 text-2xl">🛒</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">نقطة البيع</h3>
                <p class="text-sm text-gray-600">إنشاء طلبات جديدة</p>
            </button>

            <button class="bg-white rounded-lg shadow-md p-6 card-hover text-center hover:shadow-lg">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-blue-600 text-2xl">📋</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">القائمة</h3>
                <p class="text-sm text-gray-600">عرض قائمة الطعام</p>
            </button>

            <button class="bg-white rounded-lg shadow-md p-6 card-hover text-center hover:shadow-lg">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-green-600 text-2xl">⚙️</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">الإدارة</h3>
                <p class="text-sm text-gray-600">إدارة النظام</p>
            </button>

            <button class="bg-white rounded-lg shadow-md p-6 card-hover text-center hover:shadow-lg">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-purple-600 text-2xl">📊</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">التقارير</h3>
                <p class="text-sm text-gray-600">إحصائيات المبيعات</p>
            </button>
        </div>

        <!-- Features Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-bold text-gray-900 mb-4">🍽️ إدارة المطعم</h3>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3">
                        <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                        <span class="text-gray-700">إدارة القائمة والأصناف</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                        <span class="text-gray-700">مناطق التوصيل</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                        <span class="text-gray-700">إدارة الكوبونات</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                        <span class="text-gray-700">إعدادات المطعم</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-bold text-gray-900 mb-4">📱 الميزات المتقدمة</h3>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3">
                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <span class="text-gray-700">نقطة بيع متكاملة</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <span class="text-gray-700">طباعة الفواتير</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <span class="text-gray-700">تخصيص الألوان والشعار</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <span class="text-gray-700">ربط وسائل التواصل</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Installation Instructions -->
        <div class="mt-12 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4 text-center">🚀 كيفية تشغيل النظام</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="font-bold">1</span>
                    </div>
                    <h4 class="font-semibold mb-2">تثبيت Node.js</h4>
                    <p class="text-sm text-gray-600">حمل وثبت Node.js من nodejs.org</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="font-bold">2</span>
                    </div>
                    <h4 class="font-semibold mb-2">تثبيت المتطلبات</h4>
                    <p class="text-sm text-gray-600">شغل npm install في مجلد المشروع</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="font-bold">3</span>
                    </div>
                    <h4 class="font-semibold mb-2">تشغيل النظام</h4>
                    <p class="text-sm text-gray-600">شغل npm run setup ثم npm run dev</p>
                </div>
            </div>
            <div class="mt-6 text-center">
                <div class="bg-white rounded-lg p-4 inline-block">
                    <p class="text-sm text-gray-600 mb-2">بيانات تسجيل الدخول الافتراضية:</p>
                    <p class="font-mono text-sm"><strong>البريد:</strong> <EMAIL></p>
                    <p class="font-mono text-sm"><strong>كلمة المرور:</strong> admin123</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-sm">نظام إدارة المطاعم المهني - جميع الحقوق محفوظة</p>
            <p class="text-xs text-gray-400 mt-2">مبني بتقنيات Next.js, React, TypeScript, Tailwind CSS</p>
        </div>
    </footer>

    <script>
        // Add some interactivity
        document.querySelectorAll('.card-hover').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-4px)';
                card.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
            });
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
                card.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
            });
        });
    </script>
</body>
</html>
