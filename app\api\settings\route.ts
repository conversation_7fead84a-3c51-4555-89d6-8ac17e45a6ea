import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { restaurantSettingsSchema } from '@/lib/validations'

export async function GET() {
  try {
    let settings = await prisma.restaurantSettings.findFirst()
    
    if (!settings) {
      // Create default settings if none exist
      settings = await prisma.restaurantSettings.create({
        data: {
          name: 'مطعم الذواقة',
          description: 'أفضل الأطباق الشرقية والغربية',
          primaryColor: '#f97316',
          secondaryColor: '#1f2937',
          accentColor: '#10b981',
          currency: 'SAR',
          taxRate: 0.15,
          deliveryEnabled: true,
          takeawayEnabled: true,
          dineInEnabled: true,
        }
      })
    }

    return NextResponse.json({ success: true, settings })
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = restaurantSettingsSchema.parse(body)

    let settings = await prisma.restaurantSettings.findFirst()
    
    if (settings) {
      settings = await prisma.restaurantSettings.update({
        where: { id: settings.id },
        data: validatedData
      })
    } else {
      settings = await prisma.restaurantSettings.create({
        data: validatedData
      })
    }

    return NextResponse.json({ success: true, settings })
  } catch (error) {
    console.error('Error updating settings:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update settings' },
      { status: 500 }
    )
  }
}
