// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      String   @default("admin")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model Category {
  id          String     @id @default(cuid())
  name        String
  description String?
  image       String?
  isActive    Boolean    @default(true)
  sortOrder   Int        @default(0)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  menuItems   MenuItem[]

  @@map("categories")
}

model MenuItem {
  id          String      @id @default(cuid())
  name        String
  description String?
  price       Float
  image       String?
  isActive    Boolean     @default(true)
  isAvailable Boolean     @default(true)
  categoryId  String
  category    Category    @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  sortOrder   Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  orderItems  OrderItem[]

  @@map("menu_items")
}

model Order {
  id             String      @id @default(cuid())
  orderNumber    String      @unique
  customerName   String?
  customerPhone  String?
  customerEmail  String?
  orderType      String      // "dine-in", "takeaway", "delivery"
  status         String      @default("pending") // "pending", "confirmed", "preparing", "ready", "delivered", "cancelled"
  subtotal       Float
  tax            Float       @default(0)
  deliveryFee    Float       @default(0)
  discount       Float       @default(0)
  total          Float
  paymentMethod  String?     // "cash", "card", "online"
  paymentStatus  String      @default("pending") // "pending", "paid", "refunded"
  notes          String?
  deliveryAddress String?
  deliveryZoneId String?
  deliveryZone   DeliveryZone? @relation(fields: [deliveryZoneId], references: [id])
  couponId       String?
  coupon         Coupon?     @relation(fields: [couponId], references: [id])
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  orderItems     OrderItem[]

  @@map("orders")
}

model OrderItem {
  id         String   @id @default(cuid())
  orderId    String
  order      Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  menuItemId String
  menuItem   MenuItem @relation(fields: [menuItemId], references: [id])
  quantity   Int
  price      Float
  notes      String?
  createdAt  DateTime @default(now())

  @@map("order_items")
}

model DeliveryZone {
  id          String  @id @default(cuid())
  name        String
  description String?
  fee         Float
  minOrder    Float   @default(0)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  orders      Order[]

  @@map("delivery_zones")
}

model Coupon {
  id          String    @id @default(cuid())
  code        String    @unique
  name        String
  description String?
  type        String    // "percentage", "fixed"
  value       Float
  minOrder    Float     @default(0)
  maxDiscount Float?
  usageLimit  Int?
  usedCount   Int       @default(0)
  isActive    Boolean   @default(true)
  validFrom   DateTime
  validUntil  DateTime
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  orders      Order[]

  @@map("coupons")
}

model RestaurantSettings {
  id              String   @id @default(cuid())
  name            String
  description     String?
  logo            String?
  address         String?
  phone           String?
  email           String?
  website         String?
  facebook        String?
  instagram       String?
  twitter         String?
  whatsapp        String?
  primaryColor    String   @default("#f97316")
  secondaryColor  String   @default("#1f2937")
  accentColor     String   @default("#10b981")
  currency        String   @default("SAR")
  taxRate         Float    @default(0.15)
  deliveryEnabled Boolean  @default(true)
  takeawayEnabled Boolean  @default(true)
  dineInEnabled   Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("restaurant_settings")
}
