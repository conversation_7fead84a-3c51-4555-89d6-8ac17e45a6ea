import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { orderSchema } from '@/lib/validations'
import { generateOrderNumber, calculateDiscount } from '@/lib/utils'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const orderType = searchParams.get('orderType')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    const where: any = {}
    if (status) where.status = status
    if (orderType) where.orderType = orderType

    const orders = await prisma.order.findMany({
      where,
      include: {
        orderItems: {
          include: {
            menuItem: true
          }
        },
        deliveryZone: true,
        coupon: true
      },
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    })

    const total = await prisma.order.count({ where })

    return NextResponse.json({ 
      success: true, 
      orders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching orders:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = orderSchema.parse(body)

    // Get restaurant settings for tax rate
    const settings = await prisma.restaurantSettings.findFirst()
    const taxRate = settings?.taxRate || 0.15

    // Calculate order totals
    let subtotal = 0
    const orderItems = []

    for (const item of validatedData.orderItems) {
      const menuItem = await prisma.menuItem.findUnique({
        where: { id: item.menuItemId }
      })
      
      if (!menuItem) {
        return NextResponse.json(
          { success: false, error: `Menu item not found: ${item.menuItemId}` },
          { status: 400 }
        )
      }

      const itemTotal = menuItem.price * item.quantity
      subtotal += itemTotal

      orderItems.push({
        menuItemId: item.menuItemId,
        quantity: item.quantity,
        price: menuItem.price,
        notes: item.notes
      })
    }

    // Apply coupon if provided
    let discount = 0
    let couponId = null
    if (validatedData.couponCode) {
      const coupon = await prisma.coupon.findUnique({
        where: { code: validatedData.couponCode.toUpperCase() }
      })

      if (coupon && coupon.isActive) {
        const now = new Date()
        if (now >= coupon.validFrom && now <= coupon.validUntil) {
          if (!coupon.usageLimit || coupon.usedCount < coupon.usageLimit) {
            if (subtotal >= coupon.minOrder) {
              discount = calculateDiscount(subtotal, coupon)
              couponId = coupon.id
            }
          }
        }
      }
    }

    // Calculate delivery fee
    let deliveryFee = 0
    if (validatedData.orderType === 'delivery' && validatedData.deliveryZoneId) {
      const deliveryZone = await prisma.deliveryZone.findUnique({
        where: { id: validatedData.deliveryZoneId }
      })
      if (deliveryZone) {
        deliveryFee = deliveryZone.fee
      }
    }

    const tax = (subtotal - discount + deliveryFee) * taxRate
    const total = subtotal - discount + deliveryFee + tax

    // Create order
    const order = await prisma.order.create({
      data: {
        orderNumber: generateOrderNumber(),
        customerName: validatedData.customerName,
        customerPhone: validatedData.customerPhone,
        customerEmail: validatedData.customerEmail,
        orderType: validatedData.orderType,
        subtotal,
        tax,
        deliveryFee,
        discount,
        total,
        paymentMethod: validatedData.paymentMethod,
        notes: validatedData.notes,
        deliveryAddress: validatedData.deliveryAddress,
        deliveryZoneId: validatedData.deliveryZoneId,
        couponId,
        orderItems: {
          create: orderItems
        }
      },
      include: {
        orderItems: {
          include: {
            menuItem: true
          }
        },
        deliveryZone: true,
        coupon: true
      }
    })

    // Update coupon usage count if applied
    if (couponId) {
      await prisma.coupon.update({
        where: { id: couponId },
        data: { usedCount: { increment: 1 } }
      })
    }

    return NextResponse.json({ success: true, order })
  } catch (error) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create order' },
      { status: 500 }
    )
  }
}
