export interface MenuItem {
  id: string
  name: string
  description?: string
  price: number
  image?: string
  isActive: boolean
  isAvailable: boolean
  categoryId: string
  category?: Category
  sortOrder: number
  createdAt: Date
  updatedAt: Date
}

export interface Category {
  id: string
  name: string
  description?: string
  image?: string
  isActive: boolean
  sortOrder: number
  createdAt: Date
  updatedAt: Date
  menuItems?: MenuItem[]
}

export interface Order {
  id: string
  orderNumber: string
  customerName?: string
  customerPhone?: string
  customerEmail?: string
  orderType: "dine-in" | "takeaway" | "delivery"
  status: "pending" | "confirmed" | "preparing" | "ready" | "delivered" | "cancelled"
  subtotal: number
  tax: number
  deliveryFee: number
  discount: number
  total: number
  paymentMethod?: "cash" | "card" | "online"
  paymentStatus: "pending" | "paid" | "refunded"
  notes?: string
  deliveryAddress?: string
  deliveryZoneId?: string
  deliveryZone?: DeliveryZone
  couponId?: string
  coupon?: Coupon
  createdAt: Date
  updatedAt: Date
  orderItems: OrderItem[]
}

export interface OrderItem {
  id: string
  orderId: string
  menuItemId: string
  menuItem: MenuItem
  quantity: number
  price: number
  notes?: string
  createdAt: Date
}

export interface DeliveryZone {
  id: string
  name: string
  description?: string
  fee: number
  minOrder: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface Coupon {
  id: string
  code: string
  name: string
  description?: string
  type: "percentage" | "fixed"
  value: number
  minOrder: number
  maxDiscount?: number
  usageLimit?: number
  usedCount: number
  isActive: boolean
  validFrom: Date
  validUntil: Date
  createdAt: Date
  updatedAt: Date
}

export interface RestaurantSettings {
  id: string
  name: string
  description?: string
  logo?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  facebook?: string
  instagram?: string
  twitter?: string
  whatsapp?: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  currency: string
  taxRate: number
  deliveryEnabled: boolean
  takeawayEnabled: boolean
  dineInEnabled: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CartItem {
  menuItem: MenuItem
  quantity: number
  notes?: string
}
