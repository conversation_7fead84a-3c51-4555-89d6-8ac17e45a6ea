# نظام إدارة المطاعم المهني

نظام شامل لإدارة المطاعم يتضمن نقاط البيع، إدارة القوائم، الفواتير، والإدارة الكاملة للمطعم.

## المميزات الرئيسية

### 🍽️ إدارة القائمة
- إضافة وتعديل وحذف أصناف الطعام
- تنظيم الأصناف في فئات
- إدارة الأسعار والتوفر
- رفع صور للأصناف

### 💰 نقطة البيع (POS)
- واجهة سهلة الاستخدام لأخذ الطلبات
- دعم أنواع الطلبات المختلفة (تناول في المطعم، استلام، توصيل)
- حساب الضرائب والخصومات تلقائياً
- طرق دفع متعددة

### 📋 إدارة الطلبات
- متابعة حالة الطلبات
- طباعة الفواتير
- تخزين سجل الطلبات
- إحصائيات المبيعات

### 🚚 إدارة التوصيل
- تحديد مناطق التوصيل
- حساب رسوم التوصيل
- إدارة الحد الأدنى للطلب

### 🎫 نظام الكوبونات
- إنشاء كوبونات خصم
- تحديد تواريخ الصلاحية
- حدود الاستخدام
- أنواع خصم متعددة (نسبة مئوية أو مبلغ ثابت)

### 🎨 التخصيص والعلامة التجارية
- تخصيص ألوان النظام
- إضافة شعار المطعم
- معلومات المطعم والتواصل
- ربط وسائل التواصل الاجتماعي

### 🔐 نظام المصادقة
- تسجيل دخول آمن للإدارة
- حماية البيانات
- إدارة المستخدمين

## التقنيات المستخدمة

- **Frontend**: Next.js 14, React, TypeScript
- **Styling**: Tailwind CSS
- **Database**: Prisma ORM مع SQLite (قابل للتغيير إلى PostgreSQL)
- **Authentication**: NextAuth.js
- **UI Components**: Radix UI
- **Form Handling**: React Hook Form + Zod
- **Icons**: Lucide React

## متطلبات التشغيل

- Node.js 18+ 
- npm أو yarn أو pnpm

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
npm install
```

### 2. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
npx prisma db push

# إنشاء البيانات الأولية
npx prisma db seed
```

أو يمكنك تشغيل سكريبت الإعداد:
```bash
node scripts/setup.js
```

### 3. تشغيل التطبيق
```bash
npm run dev
```

سيعمل التطبيق على `http://localhost:3000`

## بيانات تسجيل الدخول الافتراضية

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123

## هيكل المشروع

```
├── app/                    # صفحات Next.js 14 (App Router)
│   ├── api/               # API Routes
│   ├── admin/             # صفحات الإدارة
│   ├── pos/               # نقطة البيع
│   ├── menu/              # عرض القائمة
│   └── login/             # تسجيل الدخول
├── components/            # مكونات React
│   ├── ui/               # مكونات واجهة المستخدم
│   ├── admin/            # مكونات الإدارة
│   └── pos/              # مكونات نقطة البيع
├── lib/                  # مكتبات ومرافق
├── prisma/               # مخطط قاعدة البيانات
├── scripts/              # سكريبتات الإعداد
└── types/                # تعريفات TypeScript
```

## الميزات المتقدمة

### إدارة المطعم
- إعدادات شاملة للمطعم
- إدارة معلومات الاتصال
- تخصيص الألوان والمظهر
- إعدادات الضرائب والعملة

### التقارير والإحصائيات
- تقارير المبيعات اليومية والشهرية
- إحصائيات الأصناف الأكثر مبيعاً
- تتبع أداء الكوبونات

### الطباعة
- طباعة الفواتير
- تخصيص تصميم الفاتورة
- دعم الطابعات الحرارية

## التخصيص

يمكن تخصيص النظام بسهولة من خلال:

1. **الألوان**: تغيير الألوان الأساسية من إعدادات المطعم
2. **الشعار**: رفع شعار المطعم
3. **المعلومات**: تحديث معلومات المطعم والتواصل
4. **العملة**: تغيير العملة المستخدمة
5. **الضرائب**: تعديل معدل الضريبة

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى:

1. مراجعة الوثائق
2. التحقق من ملفات السجل
3. التأكد من إعدادات قاعدة البيانات

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التجاري والشخصي.

## المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

---

**ملاحظة**: هذا النظام مصمم للمطاعم الصغيرة والمتوسطة ويمكن توسيعه حسب الحاجة.
