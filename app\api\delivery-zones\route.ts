import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { deliveryZoneSchema } from '@/lib/validations'

export async function GET() {
  try {
    const deliveryZones = await prisma.deliveryZone.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json({ success: true, deliveryZones })
  } catch (error) {
    console.error('Error fetching delivery zones:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch delivery zones' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = deliveryZoneSchema.parse(body)

    const deliveryZone = await prisma.deliveryZone.create({
      data: validatedData
    })

    return NextResponse.json({ success: true, deliveryZone })
  } catch (error) {
    console.error('Error creating delivery zone:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create delivery zone' },
      { status: 500 }
    )
  }
}
