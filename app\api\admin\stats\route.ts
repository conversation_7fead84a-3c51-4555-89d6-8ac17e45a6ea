import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get today's date range
    const today = new Date()
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    // Get total orders count
    const totalOrders = await prisma.order.count()

    // Get total revenue
    const totalRevenueResult = await prisma.order.aggregate({
      _sum: {
        total: true
      },
      where: {
        status: {
          not: 'cancelled'
        }
      }
    })

    // Get today's orders count
    const todayOrders = await prisma.order.count({
      where: {
        createdAt: {
          gte: startOfDay,
          lt: endOfDay
        }
      }
    })

    // Get today's revenue
    const todayRevenueResult = await prisma.order.aggregate({
      _sum: {
        total: true
      },
      where: {
        createdAt: {
          gte: startOfDay,
          lt: endOfDay
        },
        status: {
          not: 'cancelled'
        }
      }
    })

    const stats = {
      totalOrders,
      totalRevenue: totalRevenueResult._sum.total || 0,
      todayOrders,
      todayRevenue: todayRevenueResult._sum.total || 0
    }

    return NextResponse.json({ success: true, stats })
  } catch (error) {
    console.error('Error fetching admin stats:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch stats' },
      { status: 500 }
    )
  }
}
